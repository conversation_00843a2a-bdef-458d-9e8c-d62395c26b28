//
//  MeViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/18.
//

import UIKit
import SnapKit
import ATAuthSDK
import RSA_Swift
import MJRefresh

// 个人中心
class MeViewController: BaseViewController {
    
    // MARK: - Properties
    
    // 自定义导航视图 (按钮容器)
    private let navView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 容器背景透明
        // view.alpha = 0 // 移除此行，保持容器可见
        return view
    }()
    
    // 导航栏背景视图 (用于渐变背景色)
    private let navBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .white // 导航栏背景色
        view.alpha = 0 // 初始透明
        return view
    }()
    
    // 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .plain)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        // 移除默认的内容偏移
        tableView.contentInsetAdjustmentBehavior = .never
        tableView.sectionHeaderTopPadding = 0
        tableView.register(ProfileCell.self, forCellReuseIdentifier: "ProfileCell")
        tableView.register(FunctionsCell.self, forCellReuseIdentifier: "FunctionsCell")
        tableView.register(ContentCell.self, forCellReuseIdentifier: "ContentCell")
        return tableView
    }()
    
    // 菜单按钮
    private lazy var menuButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "home_nav_btn_menu"), for: .normal)
        button.addTarget(self, action: #selector(menuButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 设置按钮
    private lazy var settingsButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "settings_icon"), for: .normal)
        button.addTarget(self, action: #selector(settingsButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // 新增：导航栏用户信息容器视图
    private let navUserInfoView: UIView = {
        let view = UIView()
        view.alpha = 0 // 初始透明
        view.backgroundColor = .clear
        return view
    }()
    
    // 新增：导航栏用户头像
    private let navAvatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 15 // 头像高度30 / 2
        imageView.image = UIImage(named: "default_avatar") // 占位图
        return imageView
    }()
    
    // 新增：导航栏用户名
    private let navUsernameLabel: UILabel = {
        let label = UILabel()
        label.text = "用户昵称" // 占位符
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 新增：存储用户信息
    private var userInfo: UserInfoResponse? // 使用 UserInfoResponse 类型
    
    // 新增：存储个人作品列表数据
    private var personalWorksList: [PersonalWorksListDetailData] = []
    // 新增：分页参数
    private var currentPage: Int = 0
    private var pageSize: Int = 10
    private var hasMoreData: Bool = true
    private var isLoading: Bool = false
    
    // 新增：个人中心头部视图
    private var profileHeaderView: ProfileHeaderView?
    
    // 定义 Section 结构体
    struct Section {
        var title: String
        var rows: [Row]
    }
    
    // 定义 Row 结构体
    struct Row {
        var title: String
        var icon: String?
        // 其他属性...
    }
    
    // 在 MeViewController 中定义 sections 数组
    private var sections: [Section] = [
        Section(title: "个人信息", rows: [Row(title: "个人信息", icon: nil)]),
        Section(title: "功能列表", rows: [Row(title: "功能列表", icon: nil)]),
        Section(title: "内容管理", rows: [Row(title: "内容管理", icon: nil)])
    ]
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 禁用自动调整滚动视图的内容偏移
        automaticallyAdjustsScrollViewInsets = false
        // 确保设置为TabBar根视图控制器
        isTabBarRootViewController = true
        // 移除下拉刷新
        // let refreshControl = UIRefreshControl()
        // refreshControl.addTarget(self, action: #selector(handleRefresh), for: .valueChanged)
        // tableView.refreshControl = refreshControl
        setupProfileHeaderView() // 优先初始化，确保后续方法安全
        setupUI()
        // 初始加载个人作品列表
        fetchPersonalWorksList()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 确保导航栏隐藏
//        navigationController?.setNavigationBarHidden(true, animated: false)
        
        // 重置内容视图的frame
//        updateTableViewConstraints()
        
        // 每次视图出现时获取最新用户信息
        fetchUserInfo()
        
        // 新增：强制设置初始 contentOffset
        // 确保在布局完成后执行，viewWillAppear 可能还不够晚，但可以先尝试
        // 如果仍然有问题，可以考虑移至 viewDidLayoutSubviews (需要注意重复调用)
        let expectedOffsetY = -tableView.contentInset.top
        if tableView.contentOffset.y != expectedOffsetY {
             tableView.contentOffset = CGPoint(x: 0, y: expectedOffsetY)
        }
        
        // 注册侧边菜单跳转通知监听
        NotificationCenter.default.addObserver(self,
            selector: #selector(navigateToViewController(_:)),
            name: NSNotification.Name("NavigateToViewControllerNotification"),
            object: nil)

        // 注册用户信息更新通知监听
        NotificationCenter.default.addObserver(self,
            selector: #selector(handleUserInfoUpdated),
            name: NSNotification.Name("UserInfoUpdatedNotification"),
            object: nil)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        // 确保TabBar显示
        if isTabBarRootViewController, let tabBarController = self.tabBarController as? CustomTabBarController {
            tabBarController.showTabBar()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 移除侧边菜单跳转通知监听
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("NavigateToViewControllerNotification"), object: nil)
        // 移除用户信息更新通知监听
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name("UserInfoUpdatedNotification"), object: nil)
    }
    
    private func setupUI() {
        // 隐藏base的导航栏，但不触发TabBar的重新布局
        showNavBar = false
        
        // 添加TableView
        view.addSubview(tableView)
        updateTableViewConstraints()
        
        // 设置 contentInset 使内容从自定义 Nav 下方开始，并适配滚动条
        // 安全距离高度+44
        
        tableView.contentInset.top = WindowUtil.safeAreaTop + 44
        tableView.scrollIndicatorInsets.top = tableView.contentInset.top // 与 contentInset.top 保持一致
        // 新增：底部增加10pt偏移，防止最后一个cell被遮挡
        tableView.contentInset.bottom += 10
  
        // 添加自定义导航视图
        view.addSubview(navView)
        navView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
            make.height.equalTo(44)
        }
        
        // 添加导航栏背景视图，位于navView下方
        view.insertSubview(navBackgroundView, belowSubview: navView)
        navBackgroundView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(navView.snp.bottom)
        }
        
        // 添加导航栏按钮
        navView.addSubview(menuButton)
        menuButton.snp.makeConstraints { make in
            make.left.equalTo(16)
            make.centerY.equalToSuperview()
            make.size.equalTo(24)
        }
        
        navView.addSubview(settingsButton)
        settingsButton.snp.makeConstraints { make in
            make.right.equalTo(-16)
            make.centerY.equalToSuperview()
            make.size.equalTo(24)
        }
        
        // 添加导航栏用户信息视图
        navView.addSubview(navUserInfoView)
        navUserInfoView.snp.makeConstraints { make in
            // 垂直居中
            make.centerY.equalToSuperview()
            // 水平居中，但限制最大宽度，避免与左右按钮重叠
            make.centerX.equalToSuperview()
            make.height.equalTo(30) // 设置容器高度，例如头像高度
            // 左右边距，确保不与按钮接触，可以根据需要调整
            make.left.greaterThanOrEqualTo(menuButton.snp.right).offset(10)
            make.right.lessThanOrEqualTo(settingsButton.snp.left).offset(-10)
        }

        // 在 navUserInfoView 中添加头像和用户名
        navUserInfoView.addSubview(navAvatarImageView)
        navUserInfoView.addSubview(navUsernameLabel)

        navAvatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(30) // 头像大小 30x30
        }

        navUsernameLabel.snp.makeConstraints { make in
            make.left.equalTo(navAvatarImageView.snp.right).offset(8) // 用户名在头像右侧，间距8
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
    }
    
    // MARK: - Actions
    
    @objc private func menuButtonTapped() {
        
        print("菜单按钮被点击")
        
        // 创建左侧菜单视图控制器
        let settingVC = LeftMenuViewController()
        
        // 预先加载视图
        settingVC.loadViewIfNeeded()
        
        // 确保视图布局已完成
        settingVC.view.layoutIfNeeded()
        
        // 预先渲染左侧菜单视图（可选，但可能会提高性能）
        UIGraphicsBeginImageContextWithOptions(settingVC.view.bounds.size, false, 0)
        settingVC.view.drawHierarchy(in: settingVC.view.bounds, afterScreenUpdates: true)
        UIGraphicsEndImageContext()
        
        // 显示侧边栏，使用无动画效果
        gy_showSide({ (config) in
            config.animationType = .translationMask // 侧边栏动画方式
            config.timeInterval = 0 // 无动画时间
            config.direction = .left // 从左边出来
            config.maskAlpha = 0.3 // 增加遮罩透明度，使点击空白区域可以关闭侧滑菜单
            config.sideRelative = 0.7 // 侧边栏宽度比例
            config.zoomOffsetRelative = 0.7
            config.zoomRelative = 0.7
        }, settingVC)
    }
    
    @objc private func settingsButtonTapped() {
        let settingVC = SettingViewController()
        navigationController?.pushViewController(settingVC, animated: true)
    }
    
    // 添加一个新方法来更新 tableView 的约束
    private func updateTableViewConstraints() {
        let tabBarHeight: CGFloat
        
        if isTabBarRootViewController, let tabBarController = self.tabBarController as? CustomTabBarController, !tabBarController.customTabBar.isHidden {
            tabBarHeight = 44 + view.safeAreaInsets.bottom
        } else {
            tabBarHeight = 0
        }
        
        tableView.snp.remakeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-tabBarHeight)
        }
        
        // 强制布局更新
        view.layoutIfNeeded()
    }
    
    // 添加一个方法，用于切换到首页Tab
    func switchToHomeTab() {
        print("MeViewController: 执行切换到首页Tab")
        
        // 获取TabBarController
        if let tabBarController = self.tabBarController {
            // 打印当前状态
            print("找到TabBarController，当前选中index: \(tabBarController.selectedIndex)")
            
            // 1. 首先尝试通过标准方法切换
            tabBarController.selectedIndex = 0
            print("已设置TabBar selectedIndex = 0")
            
            // 2. 如果是自定义TabBarController，尝试使用其特定方法
            if let customTabBar = tabBarController as? CustomTabBarController {
                print("检测到CustomTabBarController，使用其selectTab方法")
                customTabBar.selectTab(at: 0)
            }
            
            // 3. 如果有特定的选中状态通知，发送通知
            NotificationCenter.default.post(
                name: NSNotification.Name("TabBarShouldSelectHomeTab"),
                object: nil,
                userInfo: ["index": 0]
            )
        } else {
            print("未找到TabBarController，无法切换到首页")
        }
    }
    
    // MARK: - Data Fetching
    
    // 修改：获取个人作品列表，只请求第一页10条
    private func fetchPersonalWorksList() {
        // 只请求第一页10条
        let page = 0
        let size = 10
        print("开始获取个人作品列表，页码: \(page), 每页数量: \(size)")
        APIManager.shared.getPersonalWorksList(page: page, size: size, state: 2) { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let response):
                if let data = response.data {
                    // 只保留前10条
                    self.personalWorksList = Array(data.list.prefix(10))
                    DispatchQueue.main.async {
                        // 刷新内容管理部分
                        self.tableView.reloadSections(IndexSet(integer: 1), with: .none)
                    }
                } else {
                    self.personalWorksList = []
                    self.tableView.reloadSections(IndexSet(integer: 1), with: .none)
                }
            case .failure(_):
                self.personalWorksList = []
                self.tableView.reloadSections(IndexSet(integer: 1), with: .none)
            }
        }
    }
    
    // 原有获取用户信息的方法
    private func fetchUserInfo() {
        APIManager.shared.getUserInfo { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let fetchedUserInfo):
                    self.userInfo = fetchedUserInfo
                    self.updateNavUserInfo(with: fetchedUserInfo)
                    self.profileHeaderView?.configure(with: self.userInfo?.data)
                case .failure(let error):
                    print("获取用户信息失败: \(error.localizedDescription)")
                    self.userInfo = nil
                    self.updateNavUserInfo(with: nil)
                    self.profileHeaderView?.configure(with: nil)
                }
            }
        }
    }
    
    // MARK: - UI Updates
    
    // 修改：更新导航栏用户信息的方法 (接受 UserInfoResponse?)
    private func updateNavUserInfo(with userInfo: UserInfoResponse?) {
        if let userData = userInfo?.data {
             // 更新头像
             if !userData.wxAvator.isEmpty, let url = URL(string: userData.wxAvator) {
                 // 假设 navAvatarImageView 已经导入 Kingfisher
                 self.navAvatarImageView.kf.setImage(with: url, placeholder: UIImage(named: "default_avatar"))
             } else {
                 self.navAvatarImageView.image = UIImage(named: "default_avatar")
             }
             // 更新用户名
             self.navUsernameLabel.text = userData.displayNickName
         } else {
             // 未登录或加载失败状态
             self.navAvatarImageView.image = UIImage(named: "default_avatar")
             self.navUsernameLabel.text = "未登录"
         }
    }
    
    // MARK: - One Click Login Helper
    
    private func isHorizontal(_ size: CGSize) -> Bool {
        return size.width > size.height
    }
    
    private func showOneClickLogin() {
//        return TXLiteAVSDK_UGC.getLicenceURL("")
    }
    
    // 移除下拉刷新方法
    // @objc private func handleRefresh() {
    //     fetchPersonalWorksList(isRefresh: true)
    // }
    
    // 新增：设置个人中心头部视图
    private func setupProfileHeaderView() {
        if profileHeaderView == nil {
            let header = ProfileHeaderView()
            let height = header.fittingHeight(for: view.bounds.width)
            header.frame = CGRect(x: 0, y: 0, width: view.bounds.width, height: height)
            tableView.tableHeaderView = header
            // 事件回调
            header.onAvatarTapped = { [weak self] in
                guard let self = self else { return }
                let image = self.profileHeaderView?.avatarImage
                let vc = ImageCropPreviewController(
                    image: image,
                    imageURL: nil,
                    cropRatio: CGSize(width: 1, height: 1),
                    confirmButtonTitle: "更换头像",
                    descriptionText: nil,
                    onImageChanged: { _, _ in }
                )
                vc.modalPresentationStyle = .fullScreen
                present(vc, animated: true)
            }
            header.onFollowingTapped = { [weak self] in
                self?.handleFollowOrFollowersTapped(selectedIndex: 0)
            }
            header.onFollowersTapped = { [weak self] in
                self?.handleFollowOrFollowersTapped(selectedIndex: 1)
            }
            header.onEditTapped = { [weak self] in
                let vc = UserInformationEditingPage()
                self?.navigationController?.pushViewController(vc, animated: true)
            }
            header.onLikesTapped = { [weak self] in
                self?.showLikesPopup()
            }
            // 我新增：二维码点击跳转个人主页
            header.onQRCodeTapped = { [weak self] in
                guard let self = self else { return }
                guard let userId = self.userInfo?.data?.customerId, !userId.isEmpty else { return }
                let vc = UserSharingViewController(userId: userId)
                self.navigationController?.pushViewController(vc, animated: true)
            }
            profileHeaderView = header
        }
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateTableHeaderViewFrame()
    }
    
    // 新增：统一管理tableHeaderView高度
    private func updateTableHeaderViewFrame() {
        guard let header = tableView.tableHeaderView else { return }
        header.setNeedsLayout()
        header.layoutIfNeeded()
        let height: CGFloat
        if let profileHeader = header as? ProfileHeaderView {
            height = profileHeader.fittingHeight(for: tableView.bounds.width)
        } else {
            height = header.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize).height
        }
        var frame = header.frame
        if frame.height != height {
            frame.size.height = height
            header.frame = frame
            tableView.tableHeaderView = header
        }
    }
    
    // 侧边菜单跳转处理
    @objc func navigateToViewController(_ notification: Notification) {
        // 只让当前TabBar选中的页面响应通知，防止重复push
        guard let tabBarController = self.tabBarController else { return }
        // 判断当前控制器是否为选中控制器（兼容导航控制器和自身）
        let isCurrentTab: Bool
        if let nav = self.navigationController {
            isCurrentTab = (tabBarController.selectedViewController === nav)
        } else {
            isCurrentTab = (tabBarController.selectedViewController === self)
        }
        guard isCurrentTab else { return }
        guard let viewController = notification.object as? UIViewController else { return }
        if let nav = self.navigationController {
            nav.pushViewController(viewController, animated: true)
        } else {
            // 兜底方案：用window的rootViewController查找
            if let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }),
               let tabBarController = window.rootViewController as? UITabBarController,
               let nav = tabBarController.selectedViewController as? UINavigationController {
                nav.pushViewController(viewController, animated: true)
            } else {
                // 兜底用present
                self.present(viewController, animated: true, completion: nil)
            }
        }
    }
}

// MARK: - UITableViewDelegate & UITableViewDataSource
extension MeViewController: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return 2  // 只剩功能列表、内容管理
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
        case 0:
            let cell = tableView.dequeueReusableCell(withIdentifier: "FunctionsCell", for: indexPath) as! FunctionsCell
            cell.orderFunctionTapped = { [weak self] index in
                self?.handleOrderFunctionTapped(at: index)
            }
            cell.commonFunctionTapped = { [weak self] index in
                self?.handleCommonFunctionTapped(at: index)
            }
            // 订单标题点击，跳转到WebView
            cell.orderHeaderTapped = { [weak self] in
                self?.openOrderWebView()
            }
            return cell
        case 1:
            let cell = tableView.dequeueReusableCell(withIdentifier: "ContentCell", for: indexPath) as! ContentCell
            // 只展示前10条
            let contentItems = convertToContentItems(from: Array(personalWorksList.prefix(10)))
            cell.updateContents(contentItems)
            return cell
        default:
            return UITableViewCell()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.section {
        case 0:
            return 340
        default:
            let cell = ContentCell(style: .default, reuseIdentifier: nil)
            cell.updateContents(convertToContentItems(from: personalWorksList))
            return cell.calculateHeight()
        }
    }
    
    // 移除section header的间距
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        switch section {
        case 0:
            return 10 // section 0 header高度为0，避免遮挡
        case 1:
            return 54
        default:
            return 54
        }
    }
    
    // 移除section footer的间距
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01
    }
    
    // 返回空view来移除header
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        if section == 0 {
            return nil // 不返回任何header view，避免遮挡tableHeaderView
        } else if section == 1 { // 修正这里，原为section==2
            let headerView = UIView()
            headerView.backgroundColor = UIColor(hex: "#F5F5F5") // 与 TableView 背景色一致

            // 内容标题
            let contentTitle: UILabel = {
                let label = UILabel()
                label.text = "内容管理"
                label.font = .systemFont(ofSize: 16, weight: .medium)
                label.textColor = UIColor(hex: "#333333")
                return label
            }()
            
            // 箭头图标
            let arrowIcon: UIImageView = {
                let imageView = UIImageView()
                imageView.image = UIImage(named: "left_menu_right_arrow")
                imageView.contentMode = .scaleAspectFit
                return imageView
            }()
            
            headerView.addSubview(contentTitle)
            headerView.addSubview(arrowIcon)
            
            contentTitle.snp.makeConstraints { make in
                make.top.equalTo(20)
                make.left.equalTo(20)
            }
            
            arrowIcon.snp.makeConstraints { make in
                make.centerY.equalTo(contentTitle)
                make.left.equalTo(contentTitle.snp.right).offset(8)
                make.size.equalTo(8)
            }
            
            // 添加点击手势
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleContentManagementHeaderTapped))
            headerView.addGestureRecognizer(tapGesture)
            
            return headerView
        } else {
            let spacingView = UIView()
            spacingView.backgroundColor = UIColor(hex: "#F5F5F5") // 与 TableView 背景色一致
            return spacingView
        }
    }
    
    // 返回空view来移除footer
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        return UIView()
    }
    
    // 处理订单功能点击
    private func handleOrderFunctionTapped(at index: Int) {
        switch index {
        case 0:
            print("点击了待付款")
            let webVC = WebViewController(path: "userPage/orderList/orderList?current=1", title: "待付款")
            navigationController?.pushViewController(webVC, animated: true)
        case 1:
            print("点击了待发货")
            let webVC = WebViewController(path: "userPage/orderList/orderList?current=2", title: "待发货")
            navigationController?.pushViewController(webVC, animated: true)
        case 2:
            print("点击了待收货")
            let webVC = WebViewController(path: "userPage/orderList/orderList?current=3", title: "待收货")
            navigationController?.pushViewController(webVC, animated: true)
        case 3:
            print("点击了待评价")
            let webVC = WebViewController(path: "userPage/orderList/orderList?current=4", title: "待评价")
            navigationController?.pushViewController(webVC, animated: true)
        case 4:
            print("点击了售后")
            let webVC = WebViewController(path: "userPage/buyAfterList/buyAfterList", title: "售后")
            navigationController?.pushViewController(webVC, animated: true)
        default:
            break
        }
    }
    
    // 处理常用功能点击
    private func handleCommonFunctionTapped(at index: Int) {
        switch index {
        case 0:
            print("点击了点赞作品")
            let vc = LikedWorksViewController()
            navigationController?.pushViewController(vc, animated: true)
        case 1:
            print("点击了我的收藏")
            let vc = MyCollectionViewController()
            navigationController?.pushViewController(vc, animated: true)
        case 2:
            print("点击了浏览记录")
            let vc = BrowsingHistoryViewController()
            navigationController?.pushViewController(vc, animated: true)
        case 3:
            print("点击了收货地址")
            //跳转到测试地址-收银台
            // let vc = GoodsCheckstandViewController()
            // navigationController?.pushViewController(vc, animated: true)
            //userPage/locaticonList/locaticonList
            let webVC = WebViewController(path: "userPage/locaticonList/locaticonList", title: "收货地址")
            navigationController?.pushViewController(webVC, animated: true)
        case 4:
            print("点击了学生认证")
            //actPage/actPupil/actPupil
            let webVC = WebViewController(path: "actPage/actPupil/actPupil", title: "学生认证")
            navigationController?.pushViewController(webVC, animated: true)
            //跳转到测试地址-选择兴趣tag
        case 5:
            print("点击了积分中心")
            //actPage/signAct/signAct
            let webVC = WebViewController(path: "actPage/signAct/signAct", title: "积分中心")
            navigationController?.pushViewController(webVC, animated: true)
        case 6:
            print("点击了分佣中心")
            //userPage/youshuMoney/youshuMoney
            let webVC = WebViewController(path: "userPage/youshuMoney/youshuMoney", title: "分佣中心")
            navigationController?.pushViewController(webVC, animated: true)
        case 7:
            print("点击了我的券包")
            //userPage/myCard/myCard
            let webVC = WebViewController(path: "userPage/myCard/myCard", title: "我的券包")
            navigationController?.pushViewController(webVC, animated: true)
        case 8:
            let vc = CreativeCenterViewController()
            navigationController?.pushViewController(vc, animated: true)
        case 9:
            print("点击了评论历史")
            let vc = CommentHistoryViewController()
            navigationController?.pushViewController(vc, animated: true)
        default:
            break
        }
    }

    // MARK: - Order WebView Navigation
    private func openOrderWebView() {
        // 直接传入 H5 路径，由 WebViewController 内部拼接域名
        let webVC = WebViewController(path: "userPage/orderList/orderList", title: "我的订单")
        navigationController?.pushViewController(webVC, animated: true)
    }
    
    func test() {
//        let newStr = encryptPassword("123456qq", completion: { result in
//            print("打印111: \(result)")
//            })
//            print("打印222: \(newStr)")
//        let newStr = APIManager.shared.encryptPassword("123456qq")
//        print("打印222: \(newStr)")
//        let testVc = PersonalTestViewController()
//        navigationController?.pushViewController(testVc, animated: true)
        
        if let tokenStr = UserDefaults.standard.string(forKey: "userToken") {
//            let urlStr = "http://192.168.10.103:8089/#/?token=\(tokenStr)"
//            let webView = WebViewController(url: URL(string:urlStr)!,title: "")
//            print("拼接好的网址是:\(urlStr)")
            let urlStr = "http://192.168.10.103:8080/#/pages/user/about/about?version=1.4.6"
            let webView = WebViewController(url: URL(string:urlStr)!,title: "")
            self.navigationController?.pushViewController(webView, animated: true)
        }
    }

    // 新增：处理内容管理 Header 点击事件
    @objc private func handleContentManagementHeaderTapped() {
        // 创建内容管理视图控制器
        let contentManagementVC = ContentManagementViewController()
        
        // 跳转到内容管理页面
        navigationController?.pushViewController(contentManagementVC, animated: true)
    }

    // 合并处理关注和粉丝点击的方法
    private func handleFollowOrFollowersTapped(selectedIndex: Int) {
        let followListVC = FollowListViewController()
        followListVC.selectedSegmentIndex = selectedIndex
        navigationController?.pushViewController(followListVC, animated: true)
    }

    // MARK: - Likes Popup
    private func showLikesPopup() {
        let likeCount = userInfo?.data?.likeNumber ?? 0
        let message = "当前共获得\(likeCount)点赞"
        InfoPopupView.show(in: view, title: nil, message: message)
    }
    
    // 在 MeViewController 中添加跳转到登录页面的方法
    private func showLoginViewController() {
        //点击头像-正确是调整到信息编辑or修改头像
//        let loginVC = LoginViewController2_0()
//
//        // 使用模态方式展示
//        loginVC.modalPresentationStyle = .fullScreen
//        present(loginVC, animated: true, completion: nil)
        
        // 也可以选择使用 push 方式
        // navigationController?.pushViewController(loginVC, animated: true)
    }
    
    // 修改：将API返回的作品数据转换为ContentItem
    private func convertToContentItems(from worksList: [PersonalWorksListDetailData]) -> [ContentItem] {
        print("转换作品列表，原始数量: \(worksList.count)")
        let items = worksList.map { work in
            // 添加更多调试信息
            print("处理作品: id=\(work.id), 标题=\(work.worksTitle)")
            print("封面图片URL=\(work.worksCoverImg)")
            
            // 检查封面图片URL是否有前缀，如果没有可能需要添加基础URL
            var coverImageUrl = work.worksCoverImg
            if !coverImageUrl.isEmpty && !coverImageUrl.hasPrefix("http") {
                // 使用正确的基础URL前缀
                coverImageUrl = "https://test-youshu.gzyoushu.com/video" + coverImageUrl
                print("添加前缀后的图片URL: \(coverImageUrl)")
            }
            
            return ContentItem(
                coverImage: coverImageUrl,
                title: work.worksTitle,
                duration: formatDuration(work.duration),
                views: work.watchNumber ?? 0,
                likes: work.likeNumber ?? 0
            )
        }
        return items
    }
    
    // 新增：格式化视频时长
    private func formatDuration(_ seconds: Int) -> String {
        if seconds <= 0 {
            return "00:00"
        }
        
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}

// MARK: - ContentItem
struct ContentItem {
    let coverImage: String
    let title: String
    let duration: String
    let views: Int
    let likes: Int
}

// MARK: - UIScrollViewDelegate
extension MeViewController: UIScrollViewDelegate {
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let offsetY = scrollView.contentOffset.y
        let profileHeaderHeight: CGFloat = 302
        let navHeight: CGFloat = 44
        let uidLabelTopY: CGFloat = 240
        let safeAreaTop = view.safeAreaInsets.top
        let fadeInEndOffset = uidLabelTopY - navHeight - safeAreaTop
        let fadeInStartOffset = fadeInEndOffset - 50
        let fadeDuration: CGFloat = 50.0
        var navAlpha: CGFloat = 0
        if fadeDuration > 0 {
            if offsetY >= fadeInStartOffset {
                navAlpha = min(1, max(0, (offsetY - fadeInStartOffset) / fadeDuration))
            }
        } else if offsetY >= fadeInEndOffset {
            navAlpha = 1
        }
        navBackgroundView.alpha = navAlpha
        navUserInfoView.alpha = navAlpha
        // --- 修改：加可选绑定保护 ---
        profileHeaderView?.updateHeaderBackgroundFrame(offsetY: offsetY, safeAreaTop: safeAreaTop, originalHeight: profileHeaderHeight)
    }
}

// MARK: - Notification Handlers
extension MeViewController {
    /// 处理用户信息更新通知
    @objc private func handleUserInfoUpdated() {
        print("[DEBUG] 收到用户信息更新通知，刷新数据")
        fetchUserInfo()
    }
}

// MARK: - Public Refresh Helper
extension MeViewController {
    /// 对外暴露的刷新方法：重新获取用户信息和个人作品列表
    func refreshData() {
        // 再次获取用户信息
        fetchUserInfo()
        // 重新拉取个人作品列表（第一页 10 条）
        fetchPersonalWorksList()
    }
}
